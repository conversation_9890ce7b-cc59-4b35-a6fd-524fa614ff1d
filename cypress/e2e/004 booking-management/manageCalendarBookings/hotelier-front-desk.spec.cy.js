import accounts from '../../../fixtures/accounts';
import bookings from '../../../fixtures/cypress_a/bookings';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic
    describe('Booking Management: Hotelier Front Desk can create bookings on the calendar', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create bookings via the calendar', () => {
            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/calendar?*'}).as('fetchCalendar');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/inventory?*'}).as('fetchInventory');
            //
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/credit-cards?*'}).as('fetchCards');
            cy.intercept({method: 'POST', url: '/hotels/*/calendar/book'}).as('createReservation');
            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
            //Create Reservations
            bookings.forEach(booking => {
                cy.get('td.fc-resourceName').contains(booking.label)
                    .parent('tr')
                    .find('td.fc-day' + booking.day_number)
                    .click({force: true})

                cy.get('form.jqiform').should('be.visible').fillCalendarModal(booking.reservation)

                // Wait for booking creation, but handle failures gracefully
                cy.wait('@createReservation').then((interception) => {
                    if (interception.response.statusCode !== 200) {
                        cy.log('Booking creation failed with status:', interception.response.statusCode)
                        cy.log('Response body:', interception.response.body)
                    }
                })

                // Optional: wait for rates refresh if booking was successful
                cy.get('body').then($body => {
                    if (!$body.text().includes('error') && !$body.text().includes('Error')) {
                        cy.wait('@fetchRates', { timeout: 10000 }).then(() => {
                            cy.log('Booking created successfully')
                        })
                    }
                })
            })
        })

        it('cancel all bookings on the calendar', () => {
            // Check if we're still logged in, if not, log in again
            cy.get('body').then($body => {
                if ($body.text().includes('Log In') || $body.find('input[name="email"]').length > 0) {
                    cy.log('Session lost, logging in again')
                    cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
                }
            })

            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');

            // Load Calendar with better error handling
            cy.get('body').then($body => {
                if ($body.find('a:contains("Calendar")').length > 0) {
                    cy.contains('a', /Calendar/).click({force: true})
                    cy.wait(['@fetchResources', '@fetchEvents', '@fetchRates'], { timeout: 30000 })
                } else {
                    cy.log('Calendar link not found, navigating directly')
                    cy.visit('/hotels/cypress-a/calendar')
                }
            })
            // Cancel Reservations
            cy.get('#calendar').cancelBookings(accounts.cypress_a.hotelier.front_desk)
            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})