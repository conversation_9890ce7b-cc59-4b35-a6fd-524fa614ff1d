import accounts from '../../../fixtures/accounts';
import bookings from '../../../fixtures/cypress_a/bookings';
import TestFilters from '../../../support/filterTests';

const filters = ['P2Sanity'];

Cypress.env('testFilters', filters);

TestFilters(filters, () => {
  // Your test logic
    describe('Booking Management: Hotelier Front Desk can create bookings on the calendar', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create bookings via the calendar', () => {
            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/calendar?*'}).as('fetchCalendar');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');
            //
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/credit-cards?*'}).as('fetchCards');
            cy.intercept({method: 'POST', url: '/hotels/*/calendar/book'}).as('createReservation');
            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
            //Create Reservations
            bookings.forEach(booking => {
                cy.get('td.fc-resourceName').contains(booking.label)
                    .parent('tr')
                    .find('td.fc-day' + booking.day_number)
                    .click({force: true})

                cy.get('form.jqiform').should('be.visible').fillCalendarModal(booking.reservation)
                    .wait(['@createReservation', '@fetchRates'])
            })
        })

        it('cancel all bookings on the calendar', () => {
            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');
            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
            // Cancel Reservations
            cy.get('#calendar').cancelBookings(accounts.cypress_a.hotelier.front_desk)
            // Load Calendar
            cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})