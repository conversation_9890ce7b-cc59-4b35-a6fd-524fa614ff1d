/**
 * Cancel all bookings where there is a 'Status Update' form available
 */
Cypress.Commands.add('cancelBookings', { prevSubject: 'element'}, (element, account) => {
    cy.get(element).then(calendar => {
        if (calendar.find('a.fc-event').length) {
            cy.get('a.fc-event').each(booking => {
                const href = booking.attr('href')
                if (href) {
                    cy.fn_safeVisit(href)
                    cy.wait(1000) // Give page time to load
                    // Loop through Col2
                    cy.get('.col2').then(($column) => {
                        // Find one with '.form' class
                        if ($column.find('.form').length > 0) {
                            // If the 'Change Status' form exists..
                            cy.get($column)
                                .find('.form form')
                                .then($form => {
                                    // ..and has the 'cancel' option
                                    cy.get($form).find('select option')
                                        .each($option => {
                                            if ($option.text() === 'cancelled') {
                                                // Cancel the Booking
                                                $form.fillUpdateBookingStatus('cancelled', account)
                                            }
                                        })
                                })
                        }
                    })
                }
            })
        }
    })
});