Cypress.on('uncaught:exception', (err, runnable) => {
    // returning false here prevents <PERSON><PERSON> from
    // failing the test on JS errors in the PMS
    console.log('Uncaught exception:', err.message)
    return false
})

import './helpers'
import './seed'

// COMMANDS
import './commands/cancelBookings'
import './commands/getIFrame'
import './commands/overwrites'
import 'cypress-iframe'
import './commands/jira'

// FORMS
import './forms/fillAvailabilityForm'
import './forms/fillBaseRatesForm'
import './forms/fillBookingEngineGuestDetailsForm'
import './forms/fillCalendarModal'
import './forms/fillChangePasswordForm'
import './forms/fillCreateCategoryForm'
import './forms/fillCreateChannelForm'
import './forms/fillCreateCorporationForm'
import './forms/fillCreateGatewayForm'
import './forms/fillCreateGroupForm'
import './forms/fillCreateGuestForm'
import './forms/fillCreateHotelForm'
import './forms/fillCreateHotelierForm'
import './forms/fillCreateOutletForm'
import './forms/fillCreatePackageForm'
import './forms/fillCreateProductForm'
import './forms/fillCreateRateForm'
import './forms/fillCreateRateHurdleForm'
import './forms/fillCreateRoomForm'
import './forms/fillCreateRoomTypeForm'
import './forms/fillCreateVenueForm'
import './forms/fillCreateVoucherForm'
import './forms/fillDeleteForm'
import './forms/fillDerivationForm'
import './forms/fillLoginForm'
import './forms/fillMultiCheckboxByLabels'
import './forms/fillRateInclusionForm'
import './forms/fillRateRestrictionCheckbox'
import './forms/fillRateRestrictionInput'
import './forms/fillRatesBulkUpdateForm'
import './forms/fillRatesPerDateForm'
import './forms/fillReactDaysOfWeek'
import './forms/fillReactDropdown'
import './forms/fillRoomTypeRate'
import './forms/fillSearchForm'
import './forms/fillUpdateBooking'
import './forms/fillUpdateBookingStatus'
import './forms/fillUpdateGatewayForm'
import './forms/fillUpdateHotelForm'
import './forms/fillUpdateHotelierForm'
import './forms/fillUpdatePackageForm'
import './forms/fillUpdateRateForm'
import './forms/fillUpdateReservationForm'
import './forms/fillUpdateVoucherForm'
import './forms/findCheckboxByLabel'
import './forms/findCheckboxByName'
import './forms/selectCheckbox'
import './forms/selectContaining'
import './forms/selectPrimaryGateway'
import './forms/selectRoom'
import './forms/selectReactCheckbox'
import './forms/fillReactDropdown'
import './forms/selectReactDropdown'
import './forms/selectReactDropdownOption'
import './forms/selectEditPencil'
import './forms/selectTrashCan'
import './forms/submitChangeOfDate'
import './forms/submitDiscountCodeForm'

// FUNCTIONS
import './functions/login'
import './functions/safeVisit'
import './functions/rand'
import './functions/afterEachJira'
import './functions/createString'

const mysql = require('cypress-mysql');
mysql.addCommands();