Cypress.Commands.add('fillCalendarModal', { prevSubject: 'element'}, (element, {

    type = 'Reservation',
    adults = 1,
    children = 0,
    inventory = 'Base Rate',
    overrideRestrictions = true,
    reservationType = 'Existing Guest',
    guestEmail = null,
    roomType = null

} = {}) => {

    return cy.wrap(element).within((element) => {

        if (roomType) {

            // Modal: Block or Reservation
            cy.log('Block or Reservation')
            cy.get('[data-jqi-name="disabled"]').should('be.visible')
            cy.get('.jqibuttons button').contains('Reservation').should('be.visible')
            cy.get('.jqibuttons button').contains('Block').should('be.visible')
            cy.get('.jqibuttons button').contains(type).click({force: true})
        }

        if (type === 'Reservation') {

            if (roomType) {

                // Modal: Room Type
                cy.log('Room Type')
                cy.get('[data-jqi-name="roomType"]').then(element => {
                    if (element.is(':visible')) {
                        return cy.wrap(element).within(() => {
                            cy.get('button.jqibutton').contains(roomType).click()
                        })
                    }
                })
            }

            // Modal: How many people will be staying?
            cy.log('How many people will be staying?')
            cy.get('select[name="numAdults"]').select(adults.toString())
            cy.get('select[name="numChildren"]').select(children.toString())
            cy.get('button[name="jqi_occupants_buttonOk"]').click({force: true}).wait('@fetchInventory')

            // Modal: Choose your rate
            cy.log('Choose your rate')
            cy.get('[data-jqi-name="occupants"]').should('not.be.visible')
            cy.get('[data-jqi-name="inventory"]').should('be.visible')
            cy.get('select[name="inventoryId"]').selectContaining(inventory)
            cy.get('button[name="jqi_inventory_buttonSelectrate"]').click({force: true}).wait('@fetchInventory')

            // Modal: Restrictions Override
            if (overrideRestrictions) {
                cy.log('Restrictions Override')
                cy.get('[data-jqi-name="restrictions"]').then(element => {
                    if (element.is(':visible')) {
                        cy.get('button[name="jqi_restrictions_buttonOverride"]').click({force: true})
                    }
                })
            }

            // Modal: Reservation Creation
            cy.log('Reservation Creation')
            cy.get('[data-jqi-name="inventory"]').should('not.be.visible')
            cy.get('[data-jqi-name="restrictions"]').should('not.be.visible')
            cy.get('[data-jqi-name="reservation"]').should('be.visible')

            if (reservationType === 'Existing Guest') {

                cy.get('button[name="jqi_reservation_buttonExistingGuest"]').click({force: true})

                // Modal: Guest email address
                cy.log('Guest email address')
                cy.get('[data-jqi-name="reservation"]').should('not.be.visible')
                cy.get('[data-jqi-name="guest"]').should('be.visible')
                cy.wait(1000) // Give modal time to fully render

                // Use the correct selector based on the actual HTML structure
                cy.get('[data-cy="guest-email"]').type(guestEmail)
                cy.get('button[name="jqi_guest_buttonConfirm"]').click({force: true}).wait(['@fetchInventory', '@fetchCards'])

                // Modal: Add a Card
                cy.log('Add a Card')
                cy.get('[data-jqi-name="guest"]').should('not.be.visible')
                cy.get('[data-jqi-name="cards"]').then($element => {
                    if ($element.is(':visible')) {
                        cy.get('select[name="cardId"]').selectContaining('I do not wish to use a credit card on record')
                        cy.get('button[name="jqi_cards_buttonOk"]').click({force: true}).wait('@fetchInventory')
                    }
                });
                cy.get('[data-jqi-name="complete"]').should('be.visible')

                // Success
                cy.get('[data-jqi-name="complete"] .jqititle').should('contain.text', 'Please Wait...')
                cy.get('[data-jqi-name="complete"] .jqimessage').should('contain.text', 'We\'re just saving these changes...')

                // COMPLETE

            } else if (reservationType === 'Existing Booking') {
                // @TODO
                // cy.get('button[name="jqi_reservation_buttonExistingBooking"]').click()
            } else if (reservationType === 'New Guest') {
                cy.get('button[name="jqi_reservation_buttonNewGuest"]').click();
            } else {
                // @TODO   Log error...
            }
        } else if (type === 'Block') {
            // @TODO
        }
    })

})