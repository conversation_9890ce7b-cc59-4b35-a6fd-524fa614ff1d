/**
 * PMS Login
 *
 * @param type
 * @param email
 * @param password
 * @param hotel_slug
 */
cy.fn_login = function (type, email, password, hotel_slug=null) {

    cy.log('fn_login', [type, email, password, hotel_slug])

    switch (type) {

    case 'guest':
        cy.visit(`/hotels/${hotel_slug}/guest/login`)
        cy.get('input[name=email]').clear().type(email)
        cy.get('input[name=password]').clear().type(password)
        cy.get('.btn-coloured[type=submit]').click()
        break;

    case 'corporation':
        cy.visit(`/hotels/${hotel_slug}/corporation/login`)
        cy.get('input[name=email]').clear().type(email)
        cy.get('input[name=password]').clear().type(password)
        cy.get('.btn-coloured[type=submit]').click()
        break;

    case 'hotelier':
        cy.visit('/login/', { failOnStatusCode: false })
        cy.get('body').then($body => {
            if ($body.text().includes('500') || $body.text().includes('Internal Server Error')) {
                cy.log('Login page returned 500 error, retrying...')
                cy.wait(2000)
                cy.visit('/login/', { failOnStatusCode: false })
            }
        })
        //cy.get('h2').should('be.visible').should('contain.text', 'Hotelier Log In')
        cy.get('#email').clear().type(email)
        cy.get('#password').clear().type(password)
        cy.get('.btn').click()
        break;
     
    case 'staff':
        cy.visit('/staff/login/')
        //cy.get('h2').should('be.visible').should('contain.text', 'Staff Log In')
        cy.get('#email').clear().type(email)
        cy.get('#password').clear().type(password)
        cy.get('.btn').click()
        break;

    case 'group':
        cy.visit('/v2/login/group-admin')
        cy.get('#username').should('exist').and('be.visible')
        cy.get('#password').should('exist').and('be.visible')
        cy.contains('button', 'Login').should('exist').and('be.visible')
        cy.get('#username').clear().type(email)
        cy.get('#password').clear().type(password)
        cy.get('button[type=submit]').click()
        break;

    }

}

cy.fn_logout = () => {
    cy.get('.logout').click()
}