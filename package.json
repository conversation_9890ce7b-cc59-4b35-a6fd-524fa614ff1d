{"name": "hotel", "version": "1.0.0", "description": "", "main": "gulpfile.js", "dependencies": {}, "devDependencies": {"cypress": "^13.17.0", "cypress-iframe": "^1.0.1", "cypress-mysql": "^1.0.4"}, "repository": {"type": "git", "url": "git+https://github.com/highlevelsoftware/hotel.git"}, "homepage": "https://github.com/highlevelsoftware/hotel#readme", "scripts": {"cy:open": "npx cypress open", "cy:localedit": "npx cypress open --env environmentName=local", "cy:Alphaedit": "npx cypress open --env environmentName=alpha", "cy:stageedit": "npx cypress open --env environmentName=staging", "cy:zbsedit": "npx cypress open --env environmentName=zbs", "cy:uatedit": "npx cypress open --env environmentName=uat", "cy:developedit": "npx cypress open --env environmentName=development", "cy:scanedit": "npx cypress open --env environmentName=scan", "cy:local": "npx cypress run --headless --env environmentName=local", "cy:stage": "npx cypress run --headless --env environmentName=staging", "cy:zbs": "npx cypress run --headless --env environmentName=zbs", "cy:uat": "npx cypress run --headless --env environmentName=uat", "cy:develop": "npx cypress run --headless --env environmentName=development", "cy:scan": "npx cypress run --headless --env environmentName=scan", "cy:alpha": "npx cypress run --headless --env environmentName=alpha"}}